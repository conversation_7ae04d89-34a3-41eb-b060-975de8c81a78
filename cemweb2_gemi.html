<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1D FDTD Wave Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
            color: #333;
        }
        h1 {
            color: #1a237e;
        }
        #simulation-container {
            border: 2px solid #333;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            background-color: #fff;
        }
       .controls {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            gap: 20px;
            align-items: center;
        }
        button {
            padding: 10px 15px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            border-radius: 5px;
            background-color: #4CAF50;
            color: white;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        label {
            font-size: 14px;
        }
        p {
            max-width: 600px;
            text-align: center;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>

    <h1>1D Wave Simulator (FDTD)</h1>
    <p>This simulation demonstrates how an electromagnetic wave travels along a one-dimensional line. It uses the FDTD method, which solves Maxwell's equations on a grid. Click "Pulse" to introduce a wave and watch it propagate and reflect off the boundaries.</p>
    <canvas id="simulation-container" width="800" height="300"></canvas>
    <div class="controls">
        <button id="pulseBtn">Pulse</button>
        <button id="resetBtn">Reset</button>
    </div>

    <script>
        const canvas = document.getElementById('simulation-container');
        const ctx = canvas.getContext('2d');

        // --- Simulation Parameters ---
        const SIZE = 400; // Number of grid points
        let Ez = new Array(SIZE).fill(0); // Electric field array
        let Hy = new Array(SIZE).fill(0); // Magnetic field array

        // FDTD works by discretizing Maxwell's equations. The Yee grid staggers the E and H fields
        // in space and time to achieve second-order accuracy.[3, 4]
        // Here, Ez lives on integer grid points, and Hy lives on half-integer points.

        let time = 0;
        const IMP0 = 377.0; // Impedance of free space

        function resetSimulation() {
            Ez.fill(0);
            Hy.fill(0);
            time = 0;
        }

        // --- FDTD Core Logic ---
        function stepFDTD() {
            // The "leapfrog" algorithm updates H and E fields at alternating half-time steps.[5]
            // This explicit time-stepping avoids complex matrix inversions.[4]

            // 1. Update Magnetic Field (Hy)
            // This loop calculates Hy for the next half-time step based on the current Ez.
            for (let i = 0; i < SIZE - 1; i++) {
                Hy[i] = Hy[i] + (Ez[i + 1] - Ez[i]) / IMP0;
            }

            // 2. Update Electric Field (Ez)
            // This loop calculates Ez for the next full-time step based on the new Hy.
            for (let i = 1; i < SIZE - 1; i++) {
                Ez[i] = Ez[i] + (Hy[i] - Hy[i - 1]) * IMP0;
            }

            // 3. Source Condition: Introduce a pulse
            // This simulates a source exciting the field.
            const pulseWidth = 40;
            if (time < 3 * pulseWidth) {
                 const sourceValue = Math.exp(-Math.pow((time - pulseWidth) / (pulseWidth / 2), 2));
                 Ez += sourceValue;
            }
            
            // 4. Boundary Conditions
            // To simulate reflections, we enforce boundary conditions. Here, we use
            // Perfect Electric Conductor (PEC) boundaries, where the tangential E-field must be zero.[6]
            Ez = 0;
            Ez = 0;

            time++;
        }

        // --- Drawing and Animation Loop ---
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const scaleX = canvas.width / SIZE;
            const scaleY = canvas.height / 4; // Scale factor for amplitude
            const midY = canvas.height / 2;

            // Draw E-field (blue)
            ctx.beginPath();
            ctx.strokeStyle = 'blue';
            ctx.lineWidth = 2;
            ctx.moveTo(0, midY - Ez * scaleY);
            for (let i = 1; i < SIZE; i++) {
                ctx.lineTo(i * scaleX, midY - Ez[i] * scaleY);
            }
            ctx.stroke();

            // Draw H-field (red)
            ctx.beginPath();
            ctx.strokeStyle = 'red';
            ctx.lineWidth = 1.5;
            ctx.moveTo(0, midY - Hy * scaleY);
            for (let i = 1; i < SIZE; i++) {
                // Hy is staggered, so we draw it at the midpoint for visualization
                const xPos = (i - 0.5) * scaleX;
                ctx.lineTo(xPos, midY - Hy[i] * scaleY);
            }
            ctx.stroke();
            
            // Draw legend
            ctx.fillStyle = 'blue';
            ctx.fillText('E-Field', 10, 20);
            ctx.fillStyle = 'red';
            ctx.fillText('H-Field', 10, 40);
        }

        let animationFrameId;
        function animate() {
            stepFDTD();
            draw();
            animationFrameId = requestAnimationFrame(animate);
        }

        // --- User Controls ---
        document.getElementById('pulseBtn').addEventListener('click', () => {
            time = 0; // Reset time to inject a new pulse
        });

        document.getElementById('resetBtn').addEventListener('click', () => {
            resetSimulation();
            draw(); // Draw the reset state
        });

        // Start the simulation
        resetSimulation();
        animate();
    </script>

</body>
</html>
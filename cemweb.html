<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cartoon EM Academy - Learn Electromagnetics with Fun!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: sparkle 10s linear infinite;
        }

        @keyframes sparkle {
            0% { transform: translateX(-100%) translateY(-100%); }
            100% { transform: translateX(100%) translateY(100%); }
        }

        .header h1 {
            font-size: 3em;
            color: white;
            text-shadow: 3px 3px 0px #ff4757, 6px 6px 0px #3742fa;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.3em;
            color: white;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            position: relative;
            z-index: 1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .lesson-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateY(20px);
            animation: slideIn 0.8s ease-out forwards;
            position: relative;
            overflow: hidden;
        }

        .lesson-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
        }

        @keyframes slideIn {
            to { transform: translateY(0); }
        }

        .lesson-title {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .emoji {
            font-size: 1.2em;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .simulation-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 3px dashed #4ecdc4;
            position: relative;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
            background: white;
            padding: 10px 15px;
            border-radius: 25px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .control-group label {
            font-weight: bold;
            color: #2c3e50;
        }

        input[type="range"] {
            width: 100px;
            height: 5px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4ecdc4;
            cursor: pointer;
            box-shadow: 0 3px 10px rgba(0,0,0,0.3);
        }

        button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        button:active {
            transform: translateY(0);
        }

        #canvas {
            border: 3px solid #4ecdc4;
            border-radius: 10px;
            background: #000;
            display: block;
            margin: 0 auto;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
        }

        .character {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 4em;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .info-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .info-box::before {
            content: '💡';
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 2em;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .formula {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            text-align: center;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .wave-animation {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23667eea"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23667eea"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23667eea"/></svg>') repeat-x;
            animation: wave 10s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(-50px); }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            background: transparent;
            position: relative;
        }
        .nav-title {
            font-size: 1.5em;
            color: white;
            font-weight: bold;
        }
        .nav-links {
            display: flex;
            gap: 20px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            font-size: 1.1em;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background 0.2s;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.15);
        }
        .hamburger {
            display: none;
            flex-direction: column;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: none;
            border: none;
            cursor: pointer;
            z-index: 10;
        }
        .hamburger span {
            display: block;
            height: 4px;
            width: 28px;
            background: white;
            margin: 5px 0;
            border-radius: 2px;
            transition: 0.3s;
        }
        @media (max-width: 700px) {
            .navbar {
                flex-direction: row;
            }
            .nav-links {
                position: absolute;
                top: 60px;
                right: 10px;
                background: #4ecdc4;
                flex-direction: column;
                gap: 0;
                border-radius: 10px;
                box-shadow: 0 4px 16px rgba(0,0,0,0.2);
                display: none;
                min-width: 140px;
            }
            .nav-links.show {
                display: flex;
            }
            .nav-links a {
                color: white;
                padding: 12px 20px;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }
            .nav-links a:last-child {
                border-bottom: none;
            }
            .hamburger {
                display: flex;
            }
        }
        @media (max-width: 600px) {
            .container, .lesson-card, .simulation-container {
                padding: 10px !important;
                margin: 10px 0 !important;
            }
            .lesson-title {
                font-size: 1.3em;
            }
            .stat-card {
                font-size: 0.9em;
            }
            #canvas, #momCanvas {
                width: 100% !important;
                height: auto !important;
                min-width: 0 !important;
            }
        }
        input[type="range"] {
            width: 80px;
        }
        button {
            padding: 10px 18px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="navbar">
            <span class="nav-title">⚡ Cartoon EM Academy ⚡</span>
            <button class="hamburger" id="hamburgerBtn" aria-label="Menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="nav-links" id="navLinks">
                <a href="#electric">Electric Field</a>
                <a href="#mom">MoM Demo</a>
            </div>
        </div>
        <p>Learn Electromagnetics with Fun Simulations and Friendly Characters!</p>
        <div class="wave-animation"></div>
    </div>

    <div class="container">
        <div class="lesson-card">
            <div class="character">🧙‍♂️</div>
            <h2 class="lesson-title">
                <span class="emoji">⚡</span>
                Interactive Electric Field Simulation
            </h2>
            <p style="font-size: 1.1em; line-height: 1.6; margin-bottom: 20px;">
                Welcome to Professor Sparky's Electric Field Laboratory! 🔬 Watch as charged particles create beautiful 
                electric field patterns. Adjust the charges and see how the field lines dance around them!
            </p>

            <div class="simulation-container">
                <div class="controls">
                    <div class="control-group">
                        <label for="charge1">Charge 1:</label>
                        <input type="range" id="charge1" min="-5" max="5" step="0.1" value="2">
                        <span id="charge1-value">2.0</span>
                    </div>
                    <div class="control-group">
                        <label for="charge2">Charge 2:</label>
                        <input type="range" id="charge2" min="-5" max="5" step="0.1" value="-2">
                        <span id="charge2-value">-2.0</span>
                    </div>
                    <div class="control-group">
                        <label for="fieldStrength">Field Strength:</label>
                        <input type="range" id="fieldStrength" min="0.1" max="2" step="0.1" value="1">
                        <span id="fieldStrength-value">1.0</span>
                    </div>
                    <button onclick="toggleAnimation()">Start/Stop Animation</button>
                    <button onclick="resetSimulation()">Reset</button>
                </div>

                <canvas id="canvas" width="800" height="400"></canvas>

                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value" id="totalForce">0.0</div>
                        <div class="stat-label">Total Force (N)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="fieldEnergy">0.0</div>
                        <div class="stat-label">Field Energy (J)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="distance">0.0</div>
                        <div class="stat-label">Distance (m)</div>
                    </div>
                </div>
            </div>

            <div class="info-box">
                <h3>🎓 What You're Learning:</h3>
                <p>This simulation demonstrates Coulomb's Law and electric field visualization. The electric field strength at any point is determined by the charges and their distances.</p>
                <div class="formula">
                    F = k × (q₁ × q₂) / r²
                </div>
                <p>Where F is force, k is Coulomb's constant, q₁ and q₂ are the charges, and r is the distance between them.</p>
            </div>
        </div>

        <!-- MoM Enhancement Section -->
        <div class="lesson-card">
            <div class="character">🧙‍♂️</div>
            <h2 class="lesson-title">
                <span class="emoji">🧮</span>
                Method of Moments (MoM) Demo
            </h2>
            <p style="font-size: 1.1em; line-height: 1.6; margin-bottom: 20px;">
                Explore the Method of Moments (MoM) for electromagnetic analysis! Adjust the segment count and excitation voltage, then run the simulation to see charge distribution and potential along a wire.
            </p>

            <div class="simulation-container" style="margin-top:30px;">
                <div class="controls">
                    <div class="control-group">
                        <label for="momSegments">Segments:</label>
                        <input type="range" id="momSegments" min="2" max="20" step="1" value="5">
                        <span id="momSegments-value">5</span>
                    </div>
                    <div class="control-group">
                        <label for="momExcitation">Excitation (V):</label>
                        <input type="range" id="momExcitation" min="1" max="100" step="1" value="10">
                        <span id="momExcitation-value">10</span>
                    </div>
                    <button onclick="runMoM()">Run MoM</button>
                </div>
                <canvas id="momCanvas" width="800" height="150" style="background:#222; border:2px solid #4ecdc4; border-radius:8px;"></canvas>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value" id="momChargeDist">-</div>
                        <div class="stat-label">Charge Distribution (C)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="momPotential">-</div>
                        <div class="stat-label">Potential (V)</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End MoM Enhancement Section -->
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        let animationId;
        let isAnimating = false;
        let time = 0;
        
        // Charge positions and values
        let charges = [
            { x: 200, y: 200, q: 2.0, color: '#ff6b6b' },
            { x: 600, y: 200, q: -2.0, color: '#4ecdc4' }
        ];
        
        // Constants
        const k = 8.99e9; // Coulomb's constant (simplified for visualization)
        const scale = 1e-10; // Scale factor for visualization
        
        function updateControls() {
            const charge1 = parseFloat(document.getElementById('charge1').value);
            const charge2 = parseFloat(document.getElementById('charge2').value);
            const fieldStrength = parseFloat(document.getElementById('fieldStrength').value);
            
            charges[0].q = charge1;
            charges[1].q = charge2;
            charges[0].color = charge1 > 0 ? '#ff6b6b' : '#4ecdc4';
            charges[1].color = charge2 > 0 ? '#ff6b6b' : '#4ecdc4';
            
            document.getElementById('charge1-value').textContent = charge1.toFixed(1);
            document.getElementById('charge2-value').textContent = charge2.toFixed(1);
            document.getElementById('fieldStrength-value').textContent = fieldStrength.toFixed(1);
            
            updateStats();
        }
        
        function updateStats() {
            const dx = charges[1].x - charges[0].x;
            const dy = charges[1].y - charges[0].y;
            const distance = Math.sqrt(dx * dx + dy * dy) / 100; // Convert to meters
            
            const force = Math.abs(k * charges[0].q * charges[1].q / (distance * distance)) * scale;
            const energy = k * charges[0].q * charges[1].q / distance * scale;
            
            document.getElementById('totalForce').textContent = force.toFixed(2);
            document.getElementById('fieldEnergy').textContent = energy.toFixed(2);
            document.getElementById('distance').textContent = distance.toFixed(2);
        }
        
        function drawElectricField() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw field lines
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 1;
            
            for (let i = 0; i < canvas.width; i += 30) {
                for (let j = 0; j < canvas.height; j += 30) {
                    const field = calculateElectricField(i, j);
                    const magnitude = Math.sqrt(field.x * field.x + field.y * field.y);
                    
                    if (magnitude > 0.1) {
                        const normalizedX = field.x / magnitude;
                        const normalizedY = field.y / magnitude;
                        const length = Math.min(magnitude * 5, 20);
                        
                        ctx.beginPath();
                        ctx.moveTo(i, j);
                        ctx.lineTo(i + normalizedX * length, j + normalizedY * length);
                        ctx.stroke();
                        
                        // Draw arrowhead
                        const arrowSize = 3;
                        const angle = Math.atan2(normalizedY, normalizedX);
                        ctx.beginPath();
                        ctx.moveTo(i + normalizedX * length, j + normalizedY * length);
                        ctx.lineTo(
                            i + normalizedX * length - arrowSize * Math.cos(angle - Math.PI / 6),
                            j + normalizedY * length - arrowSize * Math.sin(angle - Math.PI / 6)
                        );
                        ctx.lineTo(
                            i + normalizedX * length - arrowSize * Math.cos(angle + Math.PI / 6),
                            j + normalizedY * length - arrowSize * Math.sin(angle + Math.PI / 6)
                        );
                        ctx.closePath();
                        ctx.fill();
                    }
                }
            }
            
            // Draw charges
            charges.forEach((charge, index) => {
                // Glow effect
                const gradient = ctx.createRadialGradient(charge.x, charge.y, 0, charge.x, charge.y, 40);
                gradient.addColorStop(0, charge.color + '80');
                gradient.addColorStop(1, charge.color + '00');
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(charge.x, charge.y, 40, 0, 2 * Math.PI);
                ctx.fill();
                
                // Charge circle
                ctx.fillStyle = charge.color;
                ctx.beginPath();
                ctx.arc(charge.x, charge.y, 20, 0, 2 * Math.PI);
                ctx.fill();
                
                // Charge symbol and value
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(charge.q > 0 ? '+' : '−', charge.x, charge.y - 5);
                ctx.font = '12px Arial';
                ctx.fillText(Math.abs(charge.q).toFixed(1), charge.x, charge.y + 8);
                
                // Animated sparkles for positive charges
                if (charge.q > 0) {
                    for (let i = 0; i < 5; i++) {
                        const sparkleX = charge.x + Math.cos(time * 0.02 + i * Math.PI / 2.5) * 35;
                        const sparkleY = charge.y + Math.sin(time * 0.02 + i * Math.PI / 2.5) * 35;
                        ctx.fillStyle = 'rgba(255, 255, 255, ' + (0.5 + 0.5 * Math.sin(time * 0.05 + i)) + ')';
                        ctx.beginPath();
                        ctx.arc(sparkleX, sparkleY, 2, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                }
            });
            
            // Draw force vector between charges
            const dx = charges[1].x - charges[0].x;
            const dy = charges[1].y - charges[0].y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance > 0) {
                const forceDirection = charges[0].q * charges[1].q < 0 ? 1 : -1; // Attractive vs repulsive
                const midX = (charges[0].x + charges[1].x) / 2;
                const midY = (charges[0].y + charges[1].y) / 2;
                
                ctx.strokeStyle = forceDirection > 0 ? '#ff6b6b' : '#4ecdc4';
                ctx.lineWidth = 3;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(charges[0].x, charges[0].y);
                ctx.lineTo(charges[1].x, charges[1].y);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // Force label
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(forceDirection > 0 ? 'ATTRACTIVE' : 'REPULSIVE', midX, midY - 10);
            }
        }
        
        function calculateElectricField(x, y) {
            let fieldX = 0;
            let fieldY = 0;
            
            charges.forEach(charge => {
                const dx = x - charge.x;
                const dy = y - charge.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance > 0) {
                    const fieldMagnitude = Math.abs(charge.q) / (distance * distance) * 1000;
                    const normalizedX = dx / distance;
                    const normalizedY = dy / distance;
                    
                    const sign = charge.q > 0 ? 1 : -1;
                    fieldX += sign * fieldMagnitude * normalizedX;
                    fieldY += sign * fieldMagnitude * normalizedY;
                }
            });
            
            return { x: fieldX, y: fieldY };
        }
        
        function animate() {
            time++;
            drawElectricField();
            
            if (isAnimating) {
                animationId = requestAnimationFrame(animate);
            }
        }
        
        function toggleAnimation() {
            isAnimating = !isAnimating;
            if (isAnimating) {
                animate();
            } else {
                cancelAnimationFrame(animationId);
            }
        }
        
        function resetSimulation() {
            charges[0] = { x: 200, y: 200, q: 2.0, color: '#ff6b6b' };
            charges[1] = { x: 600, y: 200, q: -2.0, color: '#4ecdc4' };
            
            document.getElementById('charge1').value = 2.0;
            document.getElementById('charge2').value = -2.0;
            document.getElementById('fieldStrength').value = 1.0;
            
            updateControls();
            drawElectricField();
        }
        
        // MoM Enhancement
        const momCanvas = document.getElementById('momCanvas');
        const momCtx = momCanvas.getContext('2d');
        let momSegments = 5;
        let momExcitation = 10;

        function updateMoMControls() {
            momSegments = parseInt(document.getElementById('momSegments').value);
            momExcitation = parseFloat(document.getElementById('momExcitation').value);
            document.getElementById('momSegments-value').textContent = momSegments;
            document.getElementById('momExcitation-value').textContent = momExcitation;
        }

        document.getElementById('momSegments').addEventListener('input', updateMoMControls);
        document.getElementById('momExcitation').addEventListener('input', updateMoMControls);

        function runMoM() {
            updateMoMControls();
            // Simple MoM for a straight wire with constant excitation
            // Discretize wire into segments
            const N = momSegments;
            const V = momExcitation;
            const length = 700; // px
            const startX = 50;
            const y = 75;
            const dx = length / (N - 1);
            // Build influence matrix (self + mutual)
            let A = [];
            let b = [];
            for (let i = 0; i < N; i++) {
                A[i] = [];
                for (let j = 0; j < N; j++) {
                    if (i === j) {
                        A[i][j] = 1.0; // Self term (normalized)
                    } else {
                        // Mutual term: 1/distance
                        const dist = Math.abs(i - j) * dx + 1; // Avoid div by zero
                        A[i][j] = 1.0 / dist;
                    }
                }
                b[i] = V;
            }
            // Solve Ax = b (simple Gauss elimination for small N)
            let x = gaussianElimination(A, b);
            // Draw wire and charge distribution
            momCtx.clearRect(0, 0, momCanvas.width, momCanvas.height);
            momCtx.strokeStyle = '#4ecdc4';
            momCtx.lineWidth = 6;
            momCtx.beginPath();
            momCtx.moveTo(startX, y);
            momCtx.lineTo(startX + length, y);
            momCtx.stroke();
            // Draw charges
            for (let i = 0; i < N; i++) {
                const cx = startX + i * dx;
                const charge = x[i];
                momCtx.beginPath();
                momCtx.arc(cx, y, 10 + Math.abs(charge) * 2, 0, 2 * Math.PI);
                momCtx.fillStyle = charge > 0 ? '#ff6b6b' : '#4ecdc4';
                momCtx.globalAlpha = 0.7;
                momCtx.fill();
                momCtx.globalAlpha = 1.0;
                momCtx.fillStyle = 'white';
                momCtx.font = 'bold 12px Arial';
                momCtx.textAlign = 'center';
                momCtx.fillText(charge.toFixed(2), cx, y - 15);
            }
            // Show stats
            document.getElementById('momChargeDist').textContent = x.map(v => v.toFixed(2)).join(', ');
            document.getElementById('momPotential').textContent = V.toFixed(2);
        }

        // Simple Gaussian elimination for small systems
        function gaussianElimination(A, b) {
            const N = b.length;
            let M = [];
            for (let i = 0; i < N; i++) {
                M[i] = A[i].slice();
                M[i].push(b[i]);
            }
            for (let i = 0; i < N; i++) {
                // Pivot
                let maxRow = i;
                for (let k = i + 1; k < N; k++) {
                    if (Math.abs(M[k][i]) > Math.abs(M[maxRow][i])) maxRow = k;
                }
                let tmp = M[i];
                M[i] = M[maxRow];
                M[maxRow] = tmp;
                // Eliminate
                for (let k = i + 1; k < N; k++) {
                    let c = -M[k][i] / M[i][i];
                    for (let j = i; j < N + 1; j++) {
                        if (i === j) {
                            M[k][j] = 0;
                        } else {
                            M[k][j] += c * M[i][j];
                        }
                    }
                }
            }
            // Back substitution
            let x = Array(N).fill(0);
            for (let i = N - 1; i >= 0; i--) {
                x[i] = M[i][N] / M[i][i];
                for (let k = i - 1; k >= 0; k--) {
                    M[k][N] -= M[k][i] * x[i];
                }
            }
            return x;
        }

        // Initialize
        updateControls();
        drawElectricField();
        toggleAnimation();
        // Initialize MoM controls
        updateMoMControls();
        runMoM();

        // Hamburger menu JS
        const hamburgerBtn = document.getElementById('hamburgerBtn');
        const navLinks = document.getElementById('navLinks');
        hamburgerBtn.addEventListener('click', () => {
            navLinks.classList.toggle('show');
        });
    </script>
</body>
</html>